// 登录状态判断（假设 localStorage 里有 login 字段）
if (!localStorage.getItem('login')) {
    window.location.href = '/login.html';
}

document.addEventListener('DOMContentLoaded', function() {
    var chartDom = document.getElementById('china-map');
    var myChart = echarts.init(chartDom);

    var option = {
        geo: {
            map: 'china',
            roam: true,
            layoutCenter: ['50%', '50%'],
            layoutSize: '90%',
            itemStyle: {
                areaColor: '#e0f7fa',
                borderColor: '#111'
            },
            emphasis: {
                itemStyle: { areaColor: '#b2ebf2' }
            }
        }
    };

    // 城市经纬度（可根据需要补充）
    var geoCoordMap = {
        '喀什': [75.989, 39.467],
        '乌鲁木齐': [87.617, 43.792],
        '伊宁': [81.316, 43.922],
        '宁夏': [106.278, 38.466],
        '郑州': [113.625, 34.746],
        '信阳': [114.075, 32.123],
        '南阳': [112.528, 32.990],
        '广州': [113.264, 23.129],
        '北京': [116.407, 39.904],
        '厦门': [118.110, 24.490]
    };

    // 生成城市标注数据
    var cityData = [];
    for (var city in geoCoordMap) {
        cityData.push({
            name: city,
            value: geoCoordMap[city]
        });
    }

    // 在 option 里增加 series 配置
    option.series = [
        {
            name: '城市',
            type: 'scatter',
            coordinateSystem: 'geo',
            data: cityData,
            symbolSize: 12,
            label: {
                show: true,
                formatter: '{b}',
                position: 'right',
                color: '#ffb300',
                fontWeight: 'bold',
                fontSize: 16
            },
            itemStyle: {
                color: '#ffb300',
                borderColor: '#fff',
                borderWidth: 2,
                shadowBlur: 10,
                shadowColor: '#ffb300'
            },
            emphasis: {
                label: {
                    show: true,
                    color: '#ff9800'
                }
            }
        }
    ];

    myChart.setOption(option);
    myChart.on('click', function(params) {
        if (params.seriesType === 'scatter') {
            // 城市名转拼音（建议用一个映射表）
            var cityPinyinMap = {
                '喀什': 'kashi',
                '乌鲁木齐': 'wulumuqi',
                '伊宁': 'yining',
                '宁夏': 'ningxia',
                '郑州': 'zhengzhou',
                '信阳': 'xinyang',
                '南阳': 'nanyang',
                '广州': 'guangzhou',
                '北京': 'beijing',
                '厦门': 'xiamen'
            };
            var city = params.name;
            var pinyin = cityPinyinMap[city];
            if (pinyin) {
                window.location.href = '/table?city=' + pinyin;
            }
        }
    });
    window.addEventListener('resize', function() {
        myChart.resize();
    });
});
