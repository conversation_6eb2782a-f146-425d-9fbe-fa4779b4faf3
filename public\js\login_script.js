document.addEventListener('DOMContentLoaded', () => {
    // 页脚年份
    document.getElementById('current-year').textContent = new Date().getFullYear();

    // 表单提交处理 (示例)
    const loginForm = document.getElementById('login-form');
    if (loginForm) {
        loginForm.addEventListener('submit', (event) => {
            event.preventDefault(); // 阻止表单默认提交
            const username = event.target.username.value;
            const password = event.target.password.value;

            // 在这里添加实际的登录逻辑，例如发送AJAX请求到后端
            console.log('尝试登录:', { username, password });

            // 模拟登录成功/失败
            if (username === "admin" && password === "123") {
                localStorage.setItem('login', '1'); // 标记已登录
                window.location.href = 'index.php'; // 跳转到首页
                console.log('登录成功');
            } else {
                alert('用户名或密码错误！');
            }
        });
    }


    // 粒子背景效果
    const canvas = document.getElementById('particle-canvas');
    if (canvas) {
        const ctx = canvas.getContext('2d');
        let particlesArray;

        // 设置canvas尺寸为窗口尺寸
        canvas.width = window.innerWidth;
        canvas.height = window.innerHeight;

        // 获取CSS变量
        const primaryColor = getComputedStyle(document.documentElement).getPropertyValue('--primary-color').trim();
        const secondaryColor = getComputedStyle(document.documentElement).getPropertyValue('--secondary-color').trim();
        const particleColors = [primaryColor, secondaryColor, '#557c8a']; // 添加一个暗一点的颜色

        // 创建粒子类
        class Particle {
            constructor(x, y, directionX, directionY, size, color) {
                this.x = x;
                this.y = y;
                this.directionX = directionX;
                this.directionY = directionY;
                this.size = size;
                this.color = color;
            }

            // 绘制单个粒子
            draw() {
                ctx.beginPath();
                ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2, false);
                ctx.fillStyle = this.color;
                ctx.fill();
            }

            // 更新粒子位置，检查碰撞
            update() {
                // 检查粒子是否在屏幕范围内
                if (this.x + this.size > canvas.width || this.x - this.size < 0) {
                    this.directionX = -this.directionX;
                }
                if (this.y + this.size > canvas.height || this.y - this.size < 0) {
                    this.directionY = -this.directionY;
                }
                // 移动粒子
                this.x += this.directionX;
                this.y += this.directionY;
                this.draw();
            }
        }

        // 初始化粒子
        function initParticles() {
            particlesArray = [];
            const numberOfParticles = (canvas.height * canvas.width) / 9000; // 根据屏幕大小调整粒子数量
            for (let i = 0; i < numberOfParticles; i++) {
                const size = Math.random() * 2 + 0.5; // 粒子大小
                const x = Math.random() * (canvas.width - size * 2) + size;
                const y = Math.random() * (canvas.height - size * 2) + size;
                const directionX = (Math.random() * .4) - .2; // X轴移动速度和方向
                const directionY = (Math.random() * .4) - .2; // Y轴移动速度和方向
                const color = particleColors[Math.floor(Math.random() * particleColors.length)];
                particlesArray.push(new Particle(x, y, directionX, directionY, size, color));
            }
        }

        // 连接粒子
        function connectParticles() {
            let opacityValue = 1;
            for (let a = 0; a < particlesArray.length; a++) {
                for (let b = a; b < particlesArray.length; b++) {
                    const distance = ((particlesArray[a].x - particlesArray[b].x) * (particlesArray[a].x - particlesArray[b].x))
                                 + ((particlesArray[a].y - particlesArray[b].y) * (particlesArray[a].y - particlesArray[b].y));
                    if (distance < (canvas.width/7) * (canvas.height/7) / 10) { // 调整连接距离
                        opacityValue = 1 - (distance/20000);
                        let lineColor = particlesArray[a].color === primaryColor || particlesArray[b].color === primaryColor ? primaryColor : secondaryColor;
                        lineColor = particlesArray[a].color === secondaryColor || particlesArray[b].color === secondaryColor ? secondaryColor : lineColor;

                        ctx.strokeStyle = `rgba(${hexToRgb(lineColor).r}, ${hexToRgb(lineColor).g}, ${hexToRgb(lineColor).b}, ${opacityValue * 0.3})`; // 降低线条透明度
                        ctx.lineWidth = 0.5; // 线条粗细
                        ctx.beginPath();
                        ctx.moveTo(particlesArray[a].x, particlesArray[a].y);
                        ctx.lineTo(particlesArray[b].x, particlesArray[b].y);
                        ctx.stroke();
                    }
                }
            }
        }
        
        // 16进制颜色转RGB
        function hexToRgb(hex) {
            const shorthandRegex = /^#?([a-f\d])([a-f\d])([a-f\d])$/i;
            hex = hex.replace(shorthandRegex, function(m, r, g, b) {
                return r + r + g + g + b + b;
            });
            const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
            return result ? {
                r: parseInt(result[1], 16),
                g: parseInt(result[2], 16),
                b: parseInt(result[3], 16)
            } : null;
        }


        // 动画循环
        function animateParticles() {
            requestAnimationFrame(animateParticles);
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            for (let i = 0; i < particlesArray.length; i++) {
                particlesArray[i].update();
            }
            connectParticles();
        }

        // 窗口大小改变时重新初始化粒子
        window.addEventListener('resize', () => {
            canvas.width = window.innerWidth;
            canvas.height = window.innerHeight;
            initParticles();
        });

        // 鼠标交互 (可选，增加一点互动性)
        let mouse = {
            x: null,
            y: null,
            radius: (canvas.height/100) * (canvas.width/100) // 鼠标影响范围
        }
        window.addEventListener('mousemove', (event) => {
            mouse.x = event.clientX;
            mouse.y = event.clientY;
        });
        window.addEventListener('mouseout', () => {
            mouse.x = undefined;
            mouse.y = undefined;
        });


        initParticles();
        animateParticles();
    }
});