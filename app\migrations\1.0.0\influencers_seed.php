<?php
// 直接用命令行运行：php app/migrations/1.0.0/influencers_seed.php

$testPath = 'C:/Users/<USER>/Desktop/xjke/project/app/db/database.sqlite';
echo "测试路径: $testPath\n";
if (!file_exists($testPath)) {
    die("file_exists 检查：数据库文件不存在！\n");
}
$db = new PDO('sqlite:' . $testPath);
$db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

$dir = __DIR__ . '/../../../public/js/difang/';
$files = glob($dir . '*.js');

foreach ($files as $file) {
    $city = basename($file, '.js');
    $content = file_get_contents($file);

    // 1. 提取 return [ ... ]; 之间的内容
    if (preg_match('/return\s*\[(.*)\];/sU', $content, $matches)) {
        $arrayStr = $matches[1];

        // 2. 替换所有变量名为 "date"
        $arrayStr = preg_replace('/\b\w+DateStr\b/', 'date', $arrayStr);

        // 3. 替换单引号为双引号
        $arrayStr = str_replace("'", '"', $arrayStr);

        // 4. 把 key: value 改成 "key": value
        $arrayStr = preg_replace('/(\w+)\s*:/', '"$1":', $arrayStr);

        // 5. 去除每个对象后多余的逗号
        $arrayStr = preg_replace('/},\s*]/', '} ]', $arrayStr);

        // 6. 构造完整 json
        $json = '[' . $arrayStr . ']';

        // 7. 解析 json
        $dataArr = json_decode($json, true);

        if (!$dataArr) {
            // 输出调试信息
            echo "解析 $city 数据失败，请检查格式\n";
            // 可选：输出 $json 方便调试
            file_put_contents("debug_$city.json", $json);
            continue;
        }

        foreach ($dataArr as $row) {
            $engagement_rate = isset($row['engagementRate']) ? floatval(str_replace('%', '', $row['engagementRate'])) : null;
            $click_rate = isset($row['clickRate']) ? floatval(str_replace('%', '', $row['clickRate'])) : null;
            $conversion_rate = isset($row['conversionRate']) ? floatval(str_replace('%', '', $row['conversionRate'])) : null;

            $stmt = $db->prepare('INSERT INTO influencers (city, avatar, nickname, followers, category, engagement_rate, monthly_sales, click_rate, conversion_rate, monthly_orders, date, feature) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)');
            $stmt->execute([
                $city,
                $row['avatar'] ?? '',
                $row['nickname'] ?? '',
                $row['followers'] ?? null,
                $row['category'] ?? '',
                $engagement_rate,
                $row['monthlySales'] ?? null,
                $click_rate,
                $conversion_rate,
                $row['monthlyOrders'] ?? null,
                $row['date'] ?? '',
                $row['feature'] ?? ''
            ]);
        }
        echo "导入 $city 成功\n";
    } else {
        echo "未找到 $file 的数据\n";
    }
}
echo "全部导入完成！\n";
