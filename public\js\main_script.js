// main_script.js - 公共布局相关JS

document.addEventListener('DOMContentLoaded', function() {
    // 更新时间显示
    var updateTimeEl = document.getElementById('update-time');
    if (updateTimeEl) {
        updateTimeEl.textContent = new Date().toLocaleString();
    }

    // 退出登录
    var logoutLink = document.getElementById('logout-link');
    if (logoutLink) {
        logoutLink.addEventListener('click', function (e) {
            e.preventDefault();
            localStorage.removeItem('login');
            window.location.href = 'login.html';
        });
    }
});
