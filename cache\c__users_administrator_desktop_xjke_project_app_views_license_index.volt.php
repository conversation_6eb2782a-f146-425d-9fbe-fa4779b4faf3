<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>生成授权秘钥</title>
    <link rel="stylesheet" href="/css/main_style.css">
</head>
<body>
    <script>
        // 登录判断，未登录跳转到login.html
        if (!localStorage.getItem('login')) {
            window.location.href = 'login.html';
        }
    </script>
    <div class="console-container">
        
<main class="console-main">
    <div class="license-section">
        <h2>授权秘钥生成器</h2>
        <div class="license-form">
            <div class="form-group">
                <label for="expireDate">过期日期：</label>
                <input type="date" id="expireDate" class="form-control">
            </div>
            <button id="generateBtn" class="btn">生成秘钥</button>

            <div id="result-container" style="display:none; margin-top:20px;">
                <h3>生成的秘钥：</h3>
                <div id="license-result" class="license-key-box"></div>
            </div>
        </div>
    </div>
</main>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const generateBtn = document.getElementById('generateBtn');
    const expireDate = document.getElementById('expireDate');
    const resultContainer = document.getElementById('result-container');
    const licenseResult = document.getElementById('license-result');

    generateBtn.addEventListener('click', function() {
        if (!expireDate.value) {
            alert('请选择过期日期');
            return;
        }

        // 显示加载状态
        generateBtn.disabled = true;
        generateBtn.textContent = '生成中...';

        fetch('/generate-license', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                expire_date: expireDate.value
            })
        })
        .then(response => {
            // 检查HTTP状态
            if (!response.ok) {
                return response.json().then(errorData => {
                    throw new Error(errorData.error || '服务器响应错误: ' + response.status);
                }).catch(() => {
                    throw new Error('服务器响应错误: ' + response.status);
                });
            }
            return response.json();
        })
        .then(data => {
            if (data.error) {
                throw new Error(data.error);
            }
            resultContainer.style.display = 'block';
            licenseResult.textContent = data.license_key;

            // 显示成功消息
            const successMsg = document.createElement('div');
            successMsg.style.color = 'green';
            successMsg.style.marginTop = '10px';
            successMsg.textContent = '秘钥生成成功！';
            resultContainer.appendChild(successMsg);

            // 3秒后移除成功消息
            setTimeout(() => {
                if (successMsg.parentNode) {
                    successMsg.parentNode.removeChild(successMsg);
                }
            }, 3000);
        })
        .catch(error => {
            console.error('Error:', error);

            // 显示详细错误信息
            const errorMsg = error.message || '生成秘钥失败，请重试';
            alert('错误: ' + errorMsg);

            // 也在页面上显示错误
            const errorDiv = document.createElement('div');
            errorDiv.style.color = 'red';
            errorDiv.style.marginTop = '10px';
            errorDiv.style.padding = '10px';
            errorDiv.style.border = '1px solid red';
            errorDiv.style.borderRadius = '4px';
            errorDiv.style.backgroundColor = '#ffe6e6';
            errorDiv.textContent = '错误: ' + errorMsg;

            // 移除之前的错误消息
            const existingError = document.querySelector('.error-message');
            if (existingError) {
                existingError.remove();
            }

            errorDiv.className = 'error-message';
            document.querySelector('.license-form').appendChild(errorDiv);

            // 5秒后移除错误消息
            setTimeout(() => {
                if (errorDiv.parentNode) {
                    errorDiv.parentNode.removeChild(errorDiv);
                }
            }, 5000);
        })
        .finally(() => {
            // 无论成功或失败，都恢复按钮状态
            generateBtn.disabled = false;
            generateBtn.textContent = '生成秘钥';
        });
    });
});
</script>

<style>
.license-section {
    max-width: 600px;
    margin: 0 auto;
    padding: 20px;
}
.license-form {
    margin-top: 20px;
}
.form-group {
    margin-bottom: 15px;
}
.form-control {
    padding: 8px;
    width: 100%;
    max-width: 300px;
}
.btn {
    padding: 8px 16px;
    background-color: #4CAF50;
    color: white;
    border: none;
    cursor: pointer;
}
.license-key-box {
    padding: 15px;
    background-color: #f5f5f5;
    border: 1px solid #ddd;
    word-break: break-all;
    margin-top: 10px;
}
</style>

    </div>
    <footer class="console-footer">
        <p>
            内部系统，请勿公开泄露客户隐私，做到绝对商业保密,更新时间：<span id="update-time"></span>
            <a href="#" id="logout-link" style="margin-left:20px;color:#ff5252;cursor:pointer;">退出</a>
        </p>
    </footer>
    <script src="/js/main_script.js"></script>
</body>
</html>
