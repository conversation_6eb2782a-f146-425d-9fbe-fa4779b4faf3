---
description: 
globs: 
alwaysApply: true
---
1.服务端技术
1.1 windows iis
1.2 sqlite
1.3 phalcon

2.用户端和管理端分离
2.2 路由分组（推荐）
思路：
通过路由规则，将用户端和管理端的访问路径区分开，比如用户端访问 /，管理端访问 /admin。
实现方式：
用户端的控制器和视图放在默认目录，比如 app/controllers/、app/views/。
管理端的控制器和视图放在 app/controllers/Admin/、app/views/admin/。
路由文件（如 router.php）中配置 /admin 前缀的路由指向管理端控制器。

3.建立控制器,模型,数据迁移等,都是通过phalcon Developer Tools

4.视图使用
Phalcon 自带的 Volt 模板引擎（推荐）
Volt 是 Phalcon 官方自带的模板引擎，语法类似 Twig，简单易用，性能高。

与 Phalcon 框架集成度高，支持模板继承、宏、过滤器等功能。