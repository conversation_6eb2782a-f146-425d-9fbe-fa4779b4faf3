<?php
/**
 * Database initialization script
 * This script ensures the licenses table exists in the database
 */

// Set up paths
define('BASE_PATH', dirname(dirname(__DIR__)));
define('APP_PATH', BASE_PATH . '/app');

// Include Phalcon autoloader
require_once BASE_PATH . '/vendor/autoload.php';

try {
    // Create database connection
    $dbPath = APP_PATH . '/db/database.sqlite';
    
    if (!file_exists($dbPath)) {
        echo "Creating database file: $dbPath\n";
        touch($dbPath);
    }
    
    $pdo = new PDO('sqlite:' . $dbPath);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Create licenses table
    $sql = "CREATE TABLE IF NOT EXISTS licenses (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        license_key TEXT UNIQUE NOT NULL,
        expire_date DATE NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )";
    
    $pdo->exec($sql);
    echo "Licenses table created/verified successfully.\n";
    
    // Check if table exists and show structure
    $result = $pdo->query("SELECT sql FROM sqlite_master WHERE type='table' AND name='licenses'");
    $tableInfo = $result->fetch(PDO::FETCH_ASSOC);
    
    if ($tableInfo) {
        echo "Table structure:\n";
        echo $tableInfo['sql'] . "\n";
    } else {
        echo "Warning: licenses table not found!\n";
    }
    
    // Test insert and delete to verify table works
    echo "Testing table functionality...\n";
    $testKey = 'test_' . time();
    $testDate = date('Y-m-d');
    
    $stmt = $pdo->prepare("INSERT INTO licenses (license_key, expire_date) VALUES (?, ?)");
    $stmt->execute([$testKey, $testDate]);
    echo "Test insert successful.\n";
    
    $stmt = $pdo->prepare("DELETE FROM licenses WHERE license_key = ?");
    $stmt->execute([$testKey]);
    echo "Test delete successful.\n";
    
    echo "Database initialization completed successfully!\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    exit(1);
}
