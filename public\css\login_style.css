:root {
    --primary-color: #00ffff; /* 青色 */
    --secondary-color: #00ff00; /* 亮绿色 */
    --accent-color: #f0ff00; /* 亮黄色，用于次要高亮 */
    --background-color: #0a192f; /* 深蓝背景 */
    --card-background: #172a45; /* 卡片背景 */
    --text-color: #ccd6f6;
    --text-light: #8892b0;
    --border-color: #233554;
    --glow-color: rgba(0, 255, 255, 0.3);
    --glow-secondary-color: rgba(0, 255, 0, 0.3);
    --input-bg: rgba(255, 255, 255, 0.05);
}

body {
    margin: 0;
    font-family: 'Noto Sans SC', 'Roboto Mono', monospace;
    background-color: var(--background-color);
    color: var(--text-color);
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    overflow: hidden; /* 隐藏粒子超出部分 */
    position: relative; /* 为canvas定位 */
}

#particle-canvas {
    position: fixed; /* 或者 absolute */
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 0; /* 在登录框后面 */
}

.login-container {
    position: relative; /* 确保登录框在canvas之上 */
    z-index: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20px;
}

.login-box {
    background-color: var(--card-background);
    padding: 30px 40px;
    border-radius: 12px;
    box-shadow: 0 0 25px rgba(0, 0, 0, 0.5), 0 0 15px var(--glow-color) inset;
    border: 1px solid var(--border-color);
    width: 100%;
    max-width: 400px;
    text-align: center;
    animation: fadeIn 0.8s ease-out;
    position: relative; /* 新增 */
    z-index: 2;        /* 新增，确保它比 canvas (z-index: 0) 和 login-container (z-index: 1) 都高 */
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.logo-area {
    margin-bottom: 25px;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.cat-eye-logo {
    width: 80px;
    height: 40px;
    margin-bottom: 10px;
    filter: drop-shadow(0 0 5px var(--primary-color));
    animation: pulseLogo 3s infinite ease-in-out;
}

@keyframes pulseLogo {
    0%, 100% { filter: drop-shadow(0 0 5px var(--primary-color)); transform: scale(1); }
    50% { filter: drop-shadow(0 0 10px var(--secondary-color)); transform: scale(1.05); }
}


.logo-area h1 {
    font-family: 'Orbitron', 'Noto Sans SC', sans-serif;
    color: var(--primary-color);
    font-size: 1.8em;
    margin: 0;
    font-weight: 700;
    letter-spacing: 1px;
}

.logo-area h1 .highlight {
    color: var(--secondary-color);
    text-shadow: 0 0 8px var(--glow-secondary-color);
}

.input-group {
    margin-bottom: 20px;
    text-align: left;
}

.input-group label {
    display: flex; /* 使用flex布局让图标和文字对齐 */
    align-items: center;
    font-size: 0.9em;
    color: var(--text-light);
    margin-bottom: 8px;
    font-weight: 500;
}
.input-group label svg {
    margin-right: 8px;
    color: var(--primary-color);
    opacity: 0.7;
}

.input-group input[type="text"],
.input-group input[type="password"] {
    width: calc(100% - 24px); /* 减去padding */
    padding: 12px;
    background-color: var(--input-bg);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    color: var(--text-color);
    font-size: 1em;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.input-group input[type="text"]:focus,
.input-group input[type="password"]:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 10px var(--glow-color);
}

.options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
    font-size: 0.85em;
}

.remember-me {
    color: var(--text-light);
    display: flex;
    align-items: center;
    cursor: pointer;
}
.remember-me input[type="checkbox"] {
    margin-right: 6px;
    accent-color: var(--primary-color); /* 改变checkbox选中颜色 */
    background-color: var(--input-bg);
    border: 1px solid var(--border-color);
    cursor: pointer;
}
.remember-me input[type="checkbox"]:checked {
    box-shadow: 0 0 5px var(--glow-color);
}


.forgot-password,
.extra-links a {
    color: var(--primary-color);
    text-decoration: none;
    transition: color 0.3s ease, text-shadow 0.3s ease;
}

.forgot-password:hover,
.extra-links a:hover {
    color: var(--secondary-color);
    text-shadow: 0 0 5px var(--glow-secondary-color);
}

.login-btn {
    width: 100%;
    padding: 12px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    border: none;
    border-radius: 6px;
    color: var(--background-color);
    font-size: 1.1em;
    font-weight: 700;
    font-family: 'Noto Sans SC', sans-serif;
    cursor: pointer;
    transition: transform 0.2s ease, box-shadow 0.3s ease;
    letter-spacing: 1px;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.2);
}

.login-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 255, 255, 0.3), 0 3px 10px rgba(0, 255, 0, 0.2);
}

.login-btn:active {
    transform: translateY(0);
}

.extra-links {
    margin-top: 20px;
    font-size: 0.9em;
    color: var(--text-light);
}

.footer-info {
    position: absolute;
    bottom: 40px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 0.8em;
    color: var(--text-light);
    opacity: 0.7;
    width: 250px;
}

/* 响应式调整 */
@media (max-width: 480px) {
    .login-box {
        padding: 25px 20px;
        margin: 15px;
    }
    .logo-area h1 {
        font-size: 1.6em;
    }
    .cat-eye-logo {
        width: 70px;
        height: 35px;
    }
}

@font-face {
    font-family: 'Orbitron';
    src: url('../fonts/Orbitron-Regular.ttf') format('truetype');
    font-weight: 400;
    font-style: normal;
}
@font-face {
    font-family: 'Orbitron';
    src: url('../fonts/Orbitron-Bold.ttf') format('truetype');
    font-weight: 700;
    font-style: normal;
}
@font-face {
    font-family: 'Noto Sans SC';
    src: url('../fonts/NotoSansSC-Regular.otf') format('opentype');
    font-weight: 400;
    font-style: normal;
}
@font-face {
    font-family: 'Noto Sans SC';
    src: url('../fonts/NotoSansSC-Bold.otf') format('opentype');
    font-weight: 700;
    font-style: normal;
}
/* 其他字重同理 */