<?php
/**
 * 测试数据库查询
 */

// Set up paths
define('BASE_PATH', __DIR__);
define('APP_PATH', BASE_PATH . '/app');

try {
    $dbPath = APP_PATH . '/db/database.sqlite';
    echo "Database path: $dbPath\n";

    $pdo = new PDO('sqlite:' . $dbPath);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // 测试查询
    $result = $pdo->query("SELECT id, license_key, expire_date, created_at FROM licenses ORDER BY id DESC");
    $licenses = $result->fetchAll(PDO::FETCH_OBJ);

    echo "Found " . count($licenses) . " licenses:\n";

    foreach ($licenses as $license) {
        echo "ID: {$license->id}, Key: {$license->license_key}, Expire: {$license->expire_date}, Created: {$license->created_at}\n";
    }

    // 如果没有数据，插入一些测试数据
    if (count($licenses) == 0) {
        echo "\nInserting test data...\n";

        $stmt = $pdo->prepare("INSERT INTO licenses (license_key, expire_date, created_at) VALUES (?, ?, datetime('now'))");

        $testData = [
            ['TEST-1234-5678-9ABC', '2024-12-31'],
            ['DEMO-ABCD-EFGH-IJKL', '2025-06-30'],
            ['SAMPLE-1111-2222-3333', '2023-12-31'] // 已过期的测试数据
        ];

        foreach ($testData as $data) {
            $stmt->execute($data);
            echo "Inserted: {$data[0]} (expires: {$data[1]})\n";
        }

        echo "Test data inserted successfully!\n";
    }

} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
