<?php

use Phalcon\Db\Column;
use Phalcon\Db\Exception;
use Phalcon\Db\Index;
use Phalcon\Db\Reference;
use Phalcon\Migrations\Mvc\Model\Migration;

/**
 * Class InfluencersMigration_100
 */
class InfluencersMigration_100 extends Migration
{
    /**
     * Define the table structure
     *
     * @return void
     * @throws Exception
     */
    public function morph(): void
    {
        $this->morphTable('influencers', [
            'columns' => [
                new Column(
                    'id',
                    [
                        'type' => Column::TYPE_INTEGER,
                        'notNull' => true,
                        'autoIncrement' => true,
                        'primary' => true,
                        'size' => 11,
                    ]
                ),
                new Column(
                    'city',
                    [
                        'type' => Column::TYPE_VARCHAR,
                        'notNull' => true,
                        'size' => 32,
                    ]
                ),
                new Column(
                    'avatar',
                    [
                        'type' => Column::TYPE_VARCHAR,
                        'notNull' => true,
                        'size' => 255,
                    ]
                ),
                new Column(
                    'nickname',
                    [
                        'type' => Column::TYPE_VARCHAR,
                        'notNull' => true,
                        'size' => 64,
                    ]
                ),
                new Column(
                    'followers',
                    [
                        'type' => Column::TYPE_INTEGER,
                        'size' => 11,
                        'notNull' => false,
                    ]
                ),
                new Column(
                    'category',
                    [
                        'type' => Column::TYPE_VARCHAR,
                        'size' => 64,
                        'notNull' => false,
                    ]
                ),
                new Column(
                    'engagement_rate',
                    [
                        'type' => Column::TYPE_FLOAT,
                        'notNull' => false,
                    ]
                ),
                new Column(
                    'monthly_sales',
                    [
                        'type' => Column::TYPE_INTEGER,
                        'size' => 11,
                        'notNull' => false,
                    ]
                ),
                new Column(
                    'click_rate',
                    [
                        'type' => Column::TYPE_FLOAT,
                        'notNull' => false,
                    ]
                ),
                new Column(
                    'conversion_rate',
                    [
                        'type' => Column::TYPE_FLOAT,
                        'notNull' => false,
                    ]
                ),
                new Column(
                    'monthly_orders',
                    [
                        'type' => Column::TYPE_INTEGER,
                        'size' => 11,
                        'notNull' => false,
                    ]
                ),
                new Column(
                    'date',
                    [
                        'type' => Column::TYPE_VARCHAR,
                        'size' => 32,
                        'notNull' => false,
                    ]
                ),
                new Column(
                    'feature',
                    [
                        'type' => Column::TYPE_TEXT,
                        'notNull' => false,
                    ]
                ),
            ],
        ]);
    }

    /**
     * Run the migrations
     *
     * @return void
     */
    public function up(): void
    {
    }

    /**
     * Reverse the migrations
     *
     * @return void
     */
    public function down(): void
    {
    }
}
