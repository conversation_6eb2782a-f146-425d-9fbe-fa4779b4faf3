<?php
declare(strict_types=1);

class LicenseController extends ControllerBase
{
    public function indexAction()
    {
        // 渲染视图
    }

    /**
     * 测试数据库连接的端点
     */
    public function testAction()
    {
        $this->response->setContentType('application/json', 'UTF-8');

        try {
            // 测试数据库连接
            $db = $this->getDI()->get('db');
            $result = $db->query("SELECT 1 as test");
            $row = $result->fetch();

            // 检查licenses表
            $tableExists = $db->tableExists('licenses');

            // 获取表结构
            $schema = $db->query("SELECT sql FROM sqlite_master WHERE type='table' AND name='licenses'")->fetch();

            return $this->response->setJsonContent([
                'database_connection' => 'OK',
                'test_query' => $row,
                'licenses_table_exists' => $tableExists,
                'table_schema' => $schema ? $schema['sql'] : 'Table not found',
                'timestamp' => date('Y-m-d H:i:s')
            ]);

        } catch (\Exception $e) {
            return $this->response->setJsonContent([
                'error' => $e->getMessage(),
                'timestamp' => date('Y-m-d H:i:s')
            ])->setStatusCode(500);
        }
    }

    public function generateLicenseAction()
    {
        // 设置响应头
        $this->response->setContentType('application/json', 'UTF-8');

        // 只接受POST请求
        if (!$this->request->isPost()) {
            return $this->response->setJsonContent(['error' => '只允许POST请求'])->setStatusCode(405);
        }

        try {
            // 获取POST数据
            $jsonData = $this->request->getJsonRawBody();

            // 检查是否为有效的JSON数据
            if (!$jsonData) {
                error_log('License generation error: Invalid JSON data received');
                return $this->response->setJsonContent(['error' => '无效的JSON数据'])->setStatusCode(400);
            }

            $expireDate = isset($jsonData->expire_date) ? $jsonData->expire_date : null;

            if (!$expireDate) {
                error_log('License generation error: Missing expire_date parameter');
                return $this->response->setJsonContent(['error' => '缺少过期日期参数'])->setStatusCode(400);
            }

            // 验证日期格式
            if (!$this->validateDate($expireDate)) {
                error_log('License generation error: Invalid date format: ' . $expireDate);
                return $this->response->setJsonContent(['error' => '日期格式无效'])->setStatusCode(400);
            }

            // 获取密钥配置
            $config = $this->getDI()->get('config');
            $secretKey = $config->application->secret_key ?? '黑猫AI-secret-key-123';

            // 生成license key (AES加密)
            $dataToEncrypt = $expireDate . $secretKey;
            $encryptMethod = 'AES-256-CBC';
            $iv = substr(hash('sha256', $secretKey), 0, 16); // 16 bytes IV

            $licenseKey = openssl_encrypt(
                $dataToEncrypt,
                $encryptMethod,
                $secretKey,
                0,
                $iv
            );

            if ($licenseKey === false) {
                error_log('License generation error: Failed to encrypt license key');
                return $this->response->setJsonContent(['error' => '加密失败'])->setStatusCode(500);
            }

            // 获取数据库连接并验证
            try {
                $db = $this->getDI()->get('db');

                // 测试数据库连接
                $db->query("SELECT 1");

            } catch (\Exception $dbError) {
                error_log('Database connection error: ' . $dbError->getMessage());
                return $this->response->setJsonContent(['error' => '数据库连接失败'])->setStatusCode(500);
            }

            // 检查licenses表是否存在
            try {
                $tableExists = $db->tableExists('licenses');
                if (!$tableExists) {
                    error_log('License generation error: licenses table does not exist');
                    return $this->response->setJsonContent(['error' => '数据库表不存在'])->setStatusCode(500);
                }
            } catch (\Exception $tableError) {
                error_log('Table check error: ' . $tableError->getMessage());
                return $this->response->setJsonContent(['error' => '数据库表检查失败'])->setStatusCode(500);
            }

            // 存储到数据库
            try {
                $success = $db->execute(
                    "INSERT INTO licenses (license_key, expire_date) VALUES (?, ?)",
                    [$licenseKey, $expireDate]
                );

                if (!$success) {
                    error_log('License generation error: Failed to insert into database');
                    return $this->response->setJsonContent(['error' => '保存授权失败'])->setStatusCode(500);
                }
            } catch (\Exception $insertError) {
                error_log('Database insert error: ' . $insertError->getMessage());
                return $this->response->setJsonContent(['error' => '数据库插入失败: ' . $insertError->getMessage()])->setStatusCode(500);
            }

            // 返回结果
            return $this->response->setJsonContent([
                'license_key' => $licenseKey,
                'expire_date' => $expireDate,
                'success' => true
            ]);

        } catch (\Exception $e) {
            // 记录详细错误信息
            error_log('License generation error: ' . $e->getMessage() . ' in ' . $e->getFile() . ' on line ' . $e->getLine());
            return $this->response->setJsonContent(['error' => '生成授权失败: ' . $e->getMessage()])->setStatusCode(500);
        }
    }

    /**
     * 验证日期格式
     */
    private function validateDate($date, $format = 'Y-m-d')
    {
        $d = \DateTime::createFromFormat($format, $date);
        return $d && $d->format($format) === $date;
    }
}
