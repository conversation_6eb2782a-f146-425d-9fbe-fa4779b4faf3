<?php
declare(strict_types=1);

class LicenseController extends ControllerBase
{
    public function indexAction()
    {
        // 渲染视图
    }

    public function generateLicenseAction()
    {
        // 关闭PHP通知和警告，防止它们污染JSON输出
        error_reporting(E_ERROR | E_PARSE);
        
        // 只接受POST请求
        if (!$this->request->isPost()) {
            return $this->response->setJsonContent(['error' => '只允许POST请求'])->setStatusCode(405);
        }
        
        try {
            // 获取POST数据
            $jsonData = $this->request->getJsonRawBody();
            
            // 检查是否为有效的JSON数据
            if (!$jsonData) {
                return $this->response->setJsonContent(['error' => '无效的JSON数据'])->setStatusCode(400);
            }
            
            $expireDate = isset($jsonData->expire_date) ? $jsonData->expire_date : null;
            
            if (!$expireDate) {
                return $this->response->setJsonContent(['error' => '缺少过期日期参数'])->setStatusCode(400);
            }
            
            // 获取密钥配置
            $config = $this->getDI()->get('config');
            $secretKey = $config->application->secret_key ?? '黑猫AI-secret-key-123';
            
            // 生成license key (AES加密)
            $dataToEncrypt = $expireDate . $secretKey;
            $encryptMethod = 'AES-256-CBC';
            $iv = substr(hash('sha256', $secretKey), 0, 16); // 16 bytes IV
            
            $licenseKey = openssl_encrypt(
                $dataToEncrypt,
                $encryptMethod,
                $secretKey,
                0,
                $iv
            );
            
            // 存储到数据库
            $db = $this->getDI()->get('db');
            $success = $db->execute(
                "INSERT INTO licenses (license_key, expire_date) VALUES (?, ?)",
                [$licenseKey, $expireDate]
            );
            
            if (!$success) {
                return $this->response->setJsonContent(['error' => '保存授权失败'])->setStatusCode(500);
            }
            
            // 返回结果
            return $this->response->setJsonContent([
                'license_key' => $licenseKey,
                'expire_date' => $expireDate
            ]);
            
        } catch (\Exception $e) {
            // 记录错误但不输出到响应中
            error_log('License generation error: ' . $e->getMessage());
            return $this->response->setJsonContent(['error' => '生成授权失败'])->setStatusCode(500);
        }
    }
}
