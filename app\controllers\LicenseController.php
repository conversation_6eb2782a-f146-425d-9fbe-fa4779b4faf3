<?php
declare(strict_types=1);

class LicenseController extends ControllerBase
{
    public function indexAction()
    {
        // 渲染视图
    }

    /**
     * 测试数据库连接的端点
     */
    public function testAction()
    {
        $this->response->setContentType('application/json', 'UTF-8');

        try {
            // 测试数据库连接
            $db = $this->getDI()->get('db');
            $result = $db->query("SELECT 1 as test");
            $row = $result->fetch();

            // 检查licenses表
            $tableExists = $db->tableExists('licenses');

            // 获取表结构
            $schema = $db->query("SELECT sql FROM sqlite_master WHERE type='table' AND name='licenses'")->fetch();

            return $this->response->setJsonContent([
                'database_connection' => 'OK',
                'test_query' => $row,
                'licenses_table_exists' => $tableExists,
                'table_schema' => $schema ? $schema['sql'] : 'Table not found',
                'timestamp' => date('Y-m-d H:i:s')
            ]);

        } catch (\Exception $e) {
            return $this->response->setJsonContent([
                'error' => $e->getMessage(),
                'timestamp' => date('Y-m-d H:i:s')
            ])->setStatusCode(500);
        }
    }

    public function generateLicenseAction()
    {
        // 设置响应头
        $this->response->setContentType('application/json', 'UTF-8');

        // 只接受POST请求
        if (!$this->request->isPost()) {
            return $this->response->setJsonContent(['error' => '只允许POST请求'])->setStatusCode(405);
        }

        try {
            // 获取POST数据
            $jsonData = $this->request->getJsonRawBody();

            // 检查是否为有效的JSON数据
            if (!$jsonData) {
                error_log('License generation error: Invalid JSON data received');
                return $this->response->setJsonContent(['error' => '无效的JSON数据'])->setStatusCode(400);
            }

            $expireDate = isset($jsonData->expire_date) ? $jsonData->expire_date : null;

            if (!$expireDate) {
                error_log('License generation error: Missing expire_date parameter');
                return $this->response->setJsonContent(['error' => '缺少过期日期参数'])->setStatusCode(400);
            }

            // 验证日期格式
            if (!$this->validateDate($expireDate)) {
                error_log('License generation error: Invalid date format: ' . $expireDate);
                return $this->response->setJsonContent(['error' => '日期格式无效'])->setStatusCode(400);
            }

            // 获取密钥配置
            $config = $this->getDI()->get('config');
            $secretKey = $config->application->secret_key ?? '黑猫AI-secret-key-123';

            // 生成简短的license key (使用MD5哈希 + 时间戳)
            $timestamp = time();
            $dataToHash = $expireDate . $secretKey . $timestamp;
            $hash = md5($dataToHash);

            // 取前16位并格式化为更易读的格式 (XXXX-XXXX-XXXX-XXXX)
            $shortKey = substr($hash, 0, 16);
            $licenseKey = strtoupper(substr($shortKey, 0, 4) . '-' . substr($shortKey, 4, 4) . '-' . substr($shortKey, 8, 4) . '-' . substr($shortKey, 12, 4));

            if (empty($licenseKey)) {
                error_log('License generation error: Failed to generate license key');
                return $this->response->setJsonContent(['error' => '生成秘钥失败'])->setStatusCode(500);
            }

            // 获取数据库连接并验证
            try {
                $db = $this->getDI()->get('db');

                // 测试数据库连接
                $db->query("SELECT 1");

            } catch (\Exception $dbError) {
                error_log('Database connection error: ' . $dbError->getMessage());
                return $this->response->setJsonContent(['error' => '数据库连接失败'])->setStatusCode(500);
            }

            // 检查licenses表是否存在
            try {
                $tableExists = $db->tableExists('licenses');
                if (!$tableExists) {
                    error_log('License generation error: licenses table does not exist');
                    return $this->response->setJsonContent(['error' => '数据库表不存在'])->setStatusCode(500);
                }
            } catch (\Exception $tableError) {
                error_log('Table check error: ' . $tableError->getMessage());
                return $this->response->setJsonContent(['error' => '数据库表检查失败'])->setStatusCode(500);
            }

            // 存储到数据库
            try {
                $success = $db->execute(
                    "INSERT INTO licenses (license_key, expire_date, created_at) VALUES (?, ?, datetime('now'))",
                    [$licenseKey, $expireDate]
                );

                if (!$success) {
                    error_log('License generation error: Failed to insert into database');
                    return $this->response->setJsonContent(['error' => '保存授权失败'])->setStatusCode(500);
                }
            } catch (\Exception $insertError) {
                error_log('Database insert error: ' . $insertError->getMessage());
                return $this->response->setJsonContent(['error' => '数据库插入失败: ' . $insertError->getMessage()])->setStatusCode(500);
            }

            // 返回结果
            return $this->response->setJsonContent([
                'license_key' => $licenseKey,
                'expire_date' => $expireDate,
                'success' => true
            ]);

        } catch (\Exception $e) {
            // 记录详细错误信息
            error_log('License generation error: ' . $e->getMessage() . ' in ' . $e->getFile() . ' on line ' . $e->getLine());
            return $this->response->setJsonContent(['error' => '生成授权失败: ' . $e->getMessage()])->setStatusCode(500);
        }
    }

    /**
     * 查看秘钥列表
     */
    public function listAction()
    {
        try {
            // 获取数据库连接
            $db = $this->getDI()->get('db');

            // 查询所有秘钥，按创建时间倒序
            $result = $db->query("SELECT id, license_key, expire_date, created_at FROM licenses ORDER BY id DESC");
            $licenses = $result->fetchAll(\Phalcon\Db::FETCH_OBJ);

            // 传递数据到视图
            $this->view->licenses = $licenses;
            $this->view->error = null; // 确保error变量被定义

        } catch (\Exception $e) {
            error_log('License list error: ' . $e->getMessage());
            $this->view->error = '获取秘钥列表失败: ' . $e->getMessage();
            $this->view->licenses = [];
        }
    }

    /**
     * 删除秘钥 (AJAX)
     */
    public function deleteAction()
    {
        $this->response->setContentType('application/json', 'UTF-8');

        if (!$this->request->isPost()) {
            return $this->response->setJsonContent(['error' => '只允许POST请求'])->setStatusCode(405);
        }

        try {
            $jsonData = $this->request->getJsonRawBody();
            $id = isset($jsonData->id) ? (int)$jsonData->id : 0;

            if ($id <= 0) {
                return $this->response->setJsonContent(['error' => '无效的ID'])->setStatusCode(400);
            }

            $db = $this->getDI()->get('db');
            $success = $db->execute("DELETE FROM licenses WHERE id = ?", [$id]);

            if ($success) {
                return $this->response->setJsonContent(['success' => true, 'message' => '删除成功']);
            } else {
                return $this->response->setJsonContent(['error' => '删除失败'])->setStatusCode(500);
            }

        } catch (\Exception $e) {
            error_log('License delete error: ' . $e->getMessage());
            return $this->response->setJsonContent(['error' => '删除失败: ' . $e->getMessage()])->setStatusCode(500);
        }
    }

    /**
     * 验证日期格式
     */
    private function validateDate($date, $format = 'Y-m-d')
    {
        $d = \DateTime::createFromFormat($format, $date);
        return $d && $d->format($format) === $date;
    }
}
