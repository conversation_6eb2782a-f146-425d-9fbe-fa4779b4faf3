<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>客户数据表格</title>
    <link rel="stylesheet" href="/css/main_style.css">
</head>
<body>
    <script>
        // 登录判断，未登录跳转到login.html
        if (!localStorage.getItem('login')) {
            window.location.href = 'login.html';
        }
    </script>
    <div class="console-container">
        
<main class="console-main">
    <div class="data-section">
        <div class="data-header">
            <div class="data-header-row">
                <div class="current-city">
                    当前城市:
                    <span id="current-city-name"></span>
                    <button id="back-btn" class="back-btn">返回</button>
                </div>
                <div class="current-date">
                    当前日期:
                    <input id="flatpickr-date" type="text" style="width:120px;" readonly>
                </div>
            </div>
        </div>
        <div class="influencer-list-container">
            <div id="influencer-list"></div>
        </div>
    </div>
</main>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // 拼音-中文映射
    var cityNameMap = {
        'kashi': '喀什', 'wulumuqi': '乌鲁木齐', 'yining': '伊宁', 'ningxia': '宁夏',
        'zhengzhou': '郑州', 'xinyang': '信阳', 'nanyang': '南阳', 'guangzhou': '广州',
        'beijing': '北京', 'xiamen': '厦门'
    };
    var city = "<?= $city ?>";
    var cityCn = cityNameMap[city] || city;
    document.getElementById('current-city-name').textContent = cityCn;

    // 获取 city 参数
    if (!city) {
        document.getElementById('influencer-list').innerHTML = '<p class="placeholder-text">未指定城市。</p>';
        return;
    }

    // 动态加载达人数据 js 文件
    function loadScript(url, callback) {
        var script = document.createElement('script');
        script.type = 'text/javascript';
        script.src = url + '?_=' + Date.now(); // 防缓存
        script.onload = function () { if (typeof callback === 'function') callback(); };
        script.onerror = function () {
            document.getElementById('influencer-list').innerHTML = '<p class="placeholder-text">未找到该城市数据文件。</p>';
        };
        document.head.appendChild(script);
    }

    // flatpickr 初始化
    var flatpickrInput = document.getElementById('flatpickr-date');
    var today = new Date();
    var todayMonthStr = today.getFullYear() + '-' + String(today.getMonth() + 1).padStart(2, '0');
    var todayDayStr = String(today.getDate()).padStart(2, '0');
    var todayFullStr = todayMonthStr + '-' + todayDayStr;
    flatpickrInput.value = todayFullStr;

    // flatpickr 月份选择
    flatpickr(flatpickrInput, {
        locale: "zh",
        disableMobile: true,
        plugins: [
            new window.monthSelectPlugin({
                shorthand: true,
                dateFormat: "Y-m", // 只输出年月
                altFormat: "Y年m月",
                theme: "light"
            })
        ],
        defaultDate: todayMonthStr,
        maxDate: todayMonthStr,
        onChange: function (selectedDates, dateStr) {
            // 选择月份后，自动补全日
            const newValue = dateStr + '-' + todayDayStr;
            setTimeout(() => {
                flatpickrInput.value = newValue;
                // 1. 先清空数据
                document.getElementById('influencer-list').innerHTML = '';
                // 2. 显示加载动画
                showLoading();
                // 3. 延迟后加载数据
                setTimeout(() => {
                    hideLoading();
                    getCurrentData(dateStr); // 传递所选年月
                }, 500);
            }, 10);
        }
    });

    // 关键：flatpickr 初始化后再设置一次 value
    setTimeout(() => {
        flatpickrInput.value = todayFullStr;
    }, 10);

    // 显示加载动画
    function showLoading() {
        let loadingEl = document.getElementById('loading-indicator');
        if (!loadingEl) {
            loadingEl = document.createElement('div');
            loadingEl.id = 'loading-indicator';
            loadingEl.className = 'loading-indicator';
            loadingEl.textContent = '数据加载中...';
            loadingEl.style.position = 'fixed';
            loadingEl.style.left = '50%';
            loadingEl.style.top = '50%';
            loadingEl.style.transform = 'translate(-50%, -50%)';
            loadingEl.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
            loadingEl.style.color = 'white';
            loadingEl.style.padding = '20px';
            loadingEl.style.borderRadius = '5px';
            loadingEl.style.zIndex = '1000';
            document.body.appendChild(loadingEl);
        }
    }
    function hideLoading() {
        let loadingEl = document.getElementById('loading-indicator');
        if (loadingEl) loadingEl.remove();
    }

    // 渲染达人数据
    function getCurrentData(selectedMonthStr) {
        var influencerListEl = document.getElementById('influencer-list');
        influencerListEl.innerHTML = '';
        var data;
        try {
            data = getData(flatpickrInput.value);
        } catch (e) {
            influencerListEl.innerHTML = '<p class="placeholder-text">未找到数据。</p>';
            return;
        }
        if (!data || !data.length) {
            influencerListEl.innerHTML = '<p class="placeholder-text">暂无数据。</p>';
            return;
        }

        // 计算月份（如 "2024-06"）
        var monthStr = selectedMonthStr || flatpickrInput.value.substring(0, 7);
        var selectedYear = parseInt(monthStr.substring(0, 4), 10);
        var selectedMonth = parseInt(monthStr.substring(5, 7), 10);

        // 渲染达人列表
        var html = '';
        data.forEach(function(item) {
            // 模拟数据变化
            var followers = parseInt(item.followers) || 0;
            var monthlyOrders = parseInt(item.monthlyOrders) || 0;
            var monthlySales = parseInt(item.monthlySales) || 0;
            var engagementRate = parseFloat(item.engagementRate) || 0;
            var clickRate = parseFloat(item.clickRate) || 0;
            var conversionRate = parseFloat(item.conversionRate) || 0;

            // 计算与当前月的差值
            var now = new Date();
            var nowMonth = now.getMonth() + 1;
            var nowYear = now.getFullYear();
            var monthDiff = (nowYear - selectedYear) * 12 + (nowMonth - selectedMonth);
            if (monthDiff !== 0) {
                // 让历史月份数据上下浮动5%~10%，正负交替
                var factor = 1 + (Math.pow(-1, monthDiff) * (0.05 + 0.01 * (monthDiff % 5)));
                followers = Math.round(followers * factor);
                monthlyOrders = Math.round(monthlyOrders * factor);
                monthlySales = Math.round(monthlySales * factor);
                engagementRate = (engagementRate * factor).toFixed(1) + '%';
                clickRate = (clickRate * factor).toFixed(1) + '%';
                conversionRate = (conversionRate * factor).toFixed(1) + '%';
            } else {
                engagementRate = engagementRate + '%';
                clickRate = clickRate + '%';
                conversionRate = conversionRate + '%';
            }

            html += '<div class="influencer-list-row">'
                + '<span><img src="/' + item.avatar + '" width="36" style="border-radius:50%;"></span>'
                + '<span>' + item.nickname + '</span>'
                + '<span>' + followers + '</span>'
                + '<span>' + (item.category || '') + '</span>'
                + '<span>' + engagementRate + '</span>'
                + '<span>' + clickRate + '</span>'
                + '<span>' + conversionRate + '</span>'
                + '<span>' + monthlyOrders + '</span>'
                + '<span>' + monthlySales + '</span>'
                + '<span>' + (item.date || '') + '</span>'
                + '</div>';
        });
        // 添加表头
        html = '<div class="influencer-list-header">'
            + '<span>头像</span>'
            + '<span>昵称</span>'
            + '<span>粉丝数</span>'
            + '<span>分类</span>'
            + '<span>互动率</span>'
            + '<span>点击率</span>'
            + '<span>转化率</span>'
            + '<span>月订单</span>'
            + '<span>月销售额</span>'
            + '<span>日期</span>'
            + '</div>' + html;
        influencerListEl.innerHTML = html;
    }

    // 页面初始化时，先加载 js 数据文件，再渲染
    loadScript('/js/difang/' + city + '.js', function() {
        getCurrentData();
    });

    document.getElementById('back-btn').onclick = function() {
        window.history.back();
        // 或 window.location.href = '/'; // 如果你想直接回首页
    };
});
</script>

<!-- 页面内容结构 -->
<!-- <h2>城市达人数据表：<span id="current-city-name"></span></h2> -->


<style>

.console-container {
    display: flex;

    flex-direction: column;
    min-height: 95vh;
    width: 100%;
    padding: 5px;
    box-sizing: border-box;
}

.console-header h1 {
    font-family: 'Orbitron', sans-serif;
    color: var(--primary-color);
    text-align: center;
    margin-bottom: 20px;
    text-shadow: 0 0 10px var(--glow-color);
    font-size: 2em;
}

.console-main {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    flex-grow: 1;
}

.map-section, .data-section {
    background-color: var(--card-background);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 0 15px rgba(0, 0, 0, 0.3);
}

.map-section {
    flex: 1;
    min-width: 400px;
}

.data-section {
    flex: 2;
    min-width: 500px;
    display: flex;
    flex-direction: column;
}

.map-title, .data-header h2 {
    color: var(--primary-color);
    margin-bottom: 15px;
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 10px;
}
#current-city-name {
    color: var(--secondary-color);
}

.map-image-container {
    position: relative;
    width: 100%;
    max-width: 600px;
    margin: 0 auto;
    aspect-ratio: 4 / 3;
    overflow: hidden;
}


.influencer-list-header, .influencer-list-row {
    display: flex;
    align-items: center;
    border-bottom: 1px solid #eee;
    padding: 6px 0;
}
.influencer-list-header {
    /* background: #f5f5f5; */
    font-weight: bold;
}
.influencer-list-header span, .influencer-list-row span {
    flex: 1;
    text-align: center;
    font-size: 14px;
}
.influencer-list-row img {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    object-fit: cover;
    box-shadow: 0 2px 8px rgba(0,0,0,0.08);
    border: 2px solid #fff0; /* 可选：加点透明描边更有质感 */
}
.placeholder-text {
    color: #bbb;
    text-align: center;
    margin: 30px 0;
}
.data-header-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 10px;
    font-size: 18px;
}
.current-city {
    font-weight: bold;
    color: #fff;
}
#current-city-name {
    color: #ffb300;
    margin-left: 6px;
}
.current-date {
    display: flex;
    align-items: center;
    font-size: 16px;
    color: #fff;
}
.current-time {
    display: flex;
    align-items: center;
    font-size: 16px;
    color: #fff;
}
.back-btn {
    margin-left: 18px;
    padding: 4px 18px;
    background: linear-gradient(90deg, #ffb300 0%, #ff9800 100%);
    color: #fff;
    border: none;
    border-radius: 16px;
    font-size: 14px;
    font-weight: bold;
    cursor: pointer;
    box-shadow: 0 2px 8px rgba(255,179,0,0.12);
    transition: background 0.2s, color 0.2s;
}
.back-btn:hover {
    background: linear-gradient(90deg, #ff9800 0%, #ffb300 100%);
    color: #222;
}
</style>

<link rel="stylesheet" href="/css/flatpickr.min.css">
<script src="/js/flatpickr.min.js"></script>
<script src="/js/flatpickr-zh.js"></script>
<script src="/js/flatpickr-monthSelect.js"></script>
<link rel="stylesheet" href="/css/flatpickr-monthSelect.css">

    </div>
    <footer class="console-footer">
        <p>
            内部系统，请勿公开泄露客户隐私，做到绝对商业保密,更新时间：<span id="update-time"></span>
            <a href="#" id="logout-link" style="margin-left:20px;color:#ff5252;cursor:pointer;">退出</a>
        </p>
    </footer>
    <script src="/js/main_script.js"></script>
</body>
</html>
