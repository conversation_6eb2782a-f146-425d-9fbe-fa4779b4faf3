<?php

use Phalcon\Db\Column;
use Phalcon\Db\Index;
use Phalcon\Db\Reference;
use Phalcon\Migrations\Mvc\Model\Migration;

class LicensesMigration_100 extends Migration
{
    public function up()
    {
        $this->morphTable(
            'licenses',
            [
                'columns' => [
                    new Column(
                        'id',
                        [
                            'type' => Column::TYPE_INTEGER,
                            'notNull' => true,
                            'autoIncrement' => true,
                            'primary' => true,
                            'size' => 11,
                        ]
                    ),
                    new Column(
                        'license_key',
                        [
                            'type' => Column::TYPE_TEXT,
                            'notNull' => true,
                            'size' => 255,
                        ]
                    ),
                    new Column(
                        'expire_date',
                        [
                            'type' => Column::TYPE_DATE,
                            'notNull' => true,
                        ]
                    ),
                ],
                'indexes' => [
                    new Index(
                        'license_key_unique',
                        ['license_key'],
                        'unique'
                    ),
                ],
            ]
        );
    }

    public function down()
    {
        $this->getConnection()->dropTable('licenses');
    }
}