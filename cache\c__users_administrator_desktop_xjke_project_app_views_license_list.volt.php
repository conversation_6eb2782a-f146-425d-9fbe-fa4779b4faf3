<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>授权秘钥列表</title>
    <link rel="stylesheet" href="/css/main_style.css">
</head>
<body>
    <script>
        // 登录判断，未登录跳转到login.html
        if (!localStorage.getItem('login')) {
            window.location.href = 'login.html';
        }
    </script>
    <div class="console-container">
        
<main class="console-main">
    <div class="license-section">
        <h2>授权秘钥列表</h2>
        
        <div class="license-nav" style="margin-bottom: 20px;">
            <a href="/license" class="btn btn-secondary">返回生成页面</a>
            <button id="refreshBtn" class="btn btn-info" style="margin-left: 10px;">刷新列表</button>
        </div>
        
        <?php if ($error) { ?>
            <div class="error-message" style="color: red; padding: 10px; border: 1px solid red; border-radius: 4px; background-color: #ffe6e6; margin-bottom: 20px;">
                <?= $error ?>
            </div>
        <?php } ?>
        
        <div class="license-list-container">
            <?php if ($licenses && $this->length($licenses) > 0) { ?>
                <div class="license-stats" style="margin-bottom: 20px; padding: 10px; background-color: #f5f5f5; border-radius: 4px;">
                    <strong>总计: <?= $this->length($licenses) ?> 个秘钥</strong>
                </div>
                
                <div class="license-table-wrapper">
                    <table class="license-table">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>授权秘钥</th>
                                <th>过期日期</th>
                                <th>创建时间</th>
                                <th>状态</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($licenses as $license) { ?>
                            <tr data-id="<?= $license->id ?>">
                                <td><?= $license->id ?></td>
                                <td class="license-key-cell">
                                    <span class="license-key"><?= $license->license_key ?></span>
                                    <button class="copy-key-btn" data-key="<?= $license->license_key ?>" title="复制秘钥">📋</button>
                                </td>
                                <td><?= $license->expire_date ?></td>
                                <td><?= ($license->created_at ? $license->created_at : '未知') ?></td>
                                <td>
                                    <?php $currentDate = date('Y-m-d'); ?>
                                    <?php if ($license->expire_date >= $currentDate) { ?>
                                        <span class="status-active">有效</span>
                                    <?php } else { ?>
                                        <span class="status-expired">已过期</span>
                                    <?php } ?>
                                </td>
                                <td>
                                    <button class="delete-btn" data-id="<?= $license->id ?>" title="删除秘钥">🗑️ 删除</button>
                                </td>
                            </tr>
                            <?php } ?>
                        </tbody>
                    </table>
                </div>
            <?php } else { ?>
                <div class="no-licenses" style="text-align: center; padding: 40px; color: #666;">
                    <p>暂无授权秘钥</p>
                    <a href="/license" class="btn">立即生成秘钥</a>
                </div>
            <?php } ?>
        </div>
    </div>
</main>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // 复制秘钥功能
    document.querySelectorAll('.copy-key-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const key = this.getAttribute('data-key');
            copyToClipboard(key, this);
        });
    });
    
    // 删除秘钥功能
    document.querySelectorAll('.delete-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const id = this.getAttribute('data-id');
            const row = this.closest('tr');
            
            if (confirm('确定要删除这个秘钥吗？此操作不可恢复。')) {
                deleteLicense(id, row);
            }
        });
    });
    
    // 刷新按钮
    document.getElementById('refreshBtn').addEventListener('click', function() {
        window.location.reload();
    });
    
    // 复制到剪贴板函数
    function copyToClipboard(text, button) {
        if (navigator.clipboard) {
            navigator.clipboard.writeText(text).then(() => {
                showCopySuccess(button);
            }).catch(err => {
                console.error('复制失败:', err);
                fallbackCopyTextToClipboard(text, button);
            });
        } else {
            fallbackCopyTextToClipboard(text, button);
        }
    }
    
    // 备用复制方法
    function fallbackCopyTextToClipboard(text, button) {
        const textArea = document.createElement("textarea");
        textArea.value = text;
        textArea.style.position = "fixed";
        textArea.style.left = "-999999px";
        textArea.style.top = "-999999px";
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();
        
        try {
            const successful = document.execCommand('copy');
            if (successful) {
                showCopySuccess(button);
            } else {
                alert('复制失败，请手动复制');
            }
        } catch (err) {
            alert('复制失败，请手动复制');
        }
        
        document.body.removeChild(textArea);
    }
    
    // 显示复制成功
    function showCopySuccess(button) {
        const originalText = button.textContent;
        button.textContent = '✅';
        button.style.backgroundColor = '#4CAF50';
        setTimeout(() => {
            button.textContent = originalText;
            button.style.backgroundColor = '';
        }, 2000);
    }
    
    // 删除秘钥函数
    function deleteLicense(id, row) {
        fetch('/license/delete', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ id: parseInt(id) })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                row.style.opacity = '0.5';
                row.style.transition = 'opacity 0.3s';
                setTimeout(() => {
                    row.remove();
                    // 如果没有更多行了，显示空状态
                    const tbody = document.querySelector('.license-table tbody');
                    if (tbody.children.length === 0) {
                        location.reload();
                    }
                }, 300);
            } else {
                alert('删除失败: ' + (data.error || '未知错误'));
            }
        })
        .catch(error => {
            console.error('删除错误:', error);
            alert('删除失败，请重试');
        });
    }
});
</script>

<style>
.license-section {
    max-width: 1000px;
    margin: 0 auto;
    padding: 20px;
}

.license-table-wrapper {
    overflow-x: auto;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.license-table {
    width: 100%;
    border-collapse: collapse;
    background-color: white;
}

.license-table th,
.license-table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #ddd;
}

.license-table th {
    background-color: #f8f9fa;
    font-weight: bold;
    color: #333;
}

.license-table tr:hover {
    background-color: #f5f5f5;
}

.license-key-cell {
    display: flex;
    align-items: center;
    gap: 8px;
}

.license-key {
    font-family: monospace;
    font-weight: bold;
    color: #2c3e50;
}

.copy-key-btn {
    background: none;
    border: 1px solid #ddd;
    border-radius: 3px;
    padding: 4px 6px;
    cursor: pointer;
    font-size: 12px;
}

.copy-key-btn:hover {
    background-color: #e9ecef;
}

.status-active {
    color: #28a745;
    font-weight: bold;
}

.status-expired {
    color: #dc3545;
    font-weight: bold;
}

.delete-btn {
    background-color: #dc3545;
    color: white;
    border: none;
    border-radius: 3px;
    padding: 6px 12px;
    cursor: pointer;
    font-size: 12px;
}

.delete-btn:hover {
    background-color: #c82333;
}

.btn {
    display: inline-block;
    padding: 8px 16px;
    background-color: #007bff;
    color: white;
    text-decoration: none;
    border-radius: 4px;
    border: none;
    cursor: pointer;
}

.btn:hover {
    background-color: #0056b3;
}

.btn-secondary {
    background-color: #6c757d;
}

.btn-secondary:hover {
    background-color: #545b62;
}

.btn-info {
    background-color: #17a2b8;
}

.btn-info:hover {
    background-color: #138496;
}

@media (max-width: 768px) {
    .license-table {
        font-size: 14px;
    }
    
    .license-table th,
    .license-table td {
        padding: 8px;
    }
}
</style>

    </div>
    <footer class="console-footer">
        <p>
            内部系统，请勿公开泄露客户隐私，做到绝对商业保密,更新时间：<span id="update-time"></span>
            <a href="#" id="logout-link" style="margin-left:20px;color:#ff5252;cursor:pointer;">退出</a>
        </p>
    </footer>
    <script src="/js/main_script.js"></script>
</body>
</html>
